import { ethers } from "hardhat";
import chalk from 'chalk';

async function debugError() {
    console.log(chalk.yellow("🔍 Debugging error 0xc831f2cb"));
    
    // Custom errors from the contract
    const errors = [
        "Unauthorized()",
        "SafeNotRegistered()",
        "SafeAlreadyRegistered()",
        "ModuleNotEnabled()",
        "InsufficientRecoveryKeys(uint256,uint256)",
        "RecoveryKeyNotFound()",
        "RecoveryKeyAlreadyUsed()",
        "RecoveryKeyExpired()",
        "InvalidProof()",
        "RecoveryNotFound()",
        "RecoveryAlreadyExecuted()",
        "RecoveryDelayNotPassed()",
        "RecoveryPaused()",
        "ActionExecutionFailed()",
        "CannotUseLastKeys()"
    ];
    
    for (const error of errors) {
        const errorHash = ethers.keccak256(ethers.toUtf8Bytes(error)).slice(0, 10);
        console.log(`${error}: ${errorHash}`);
        
        if (errorHash.toLowerCase() === "0xc831f2cb") {
            console.log(chalk.red(`❌ Found match: ${error}`));
            return;
        }
    }
    
    console.log(chalk.red("❌ Error 0xc831f2cb not found in custom errors"));
    console.log(chalk.yellow("📝 This might be a Solidity built-in error or from another contract"));
}

debugError().catch(console.error);