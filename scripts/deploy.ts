import { ethers,run } from "hardhat";
import chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';

async function main() {
  console.log(chalk.blue("\n🚀 Deploying GeoSafeRecovery Contract"));
  console.log(chalk.blue("====================================\n"));

  const [deployer] = await ethers.getSigners();
  console.log(chalk.yellow(`📧 Deploying with account: ${deployer.address}`));
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log(chalk.yellow(`💰 Account balance: ${ethers.formatEther(balance)} ETH\n`));

  // Deploy GeoSafeRecovery contract
  console.log(chalk.cyan("📦 Deploying GeoSafeRecovery..."));
  
  const GeoSafeRecovery = await ethers.getContractFactory("GeoSafeRecovery");
  
  // Use deployer address as factory for now (can be changed later)
  const geoSafeRecovery = await GeoSafeRecovery.deploy(deployer.address);
  await geoSafeRecovery.waitForDeployment();
  
  const contractAddress = await geoSafeRecovery.getAddress();
  console.log(chalk.green(`✅ GeoSafeRecovery deployed to: ${contractAddress}`));
  
  // Get deployment transaction details
  const deployTx = geoSafeRecovery.deploymentTransaction();
  if (deployTx) {
    const receipt = await deployTx.wait();
    if (receipt) {
      console.log(chalk.gray(`   📋 Transaction hash: ${receipt.hash}`));
      console.log(chalk.gray(`   ⛽ Gas used: ${receipt.gasUsed.toString()}`));
      console.log(chalk.gray(`   📊 Block number: ${receipt.blockNumber}`));
    }
  }

  // Verify contract constants
  console.log(chalk.cyan("\n🔍 Verifying contract constants..."));
  const defaultDelay = await geoSafeRecovery.DEFAULT_RECOVERY_DELAY();
  const minKeys = await geoSafeRecovery.MIN_RECOVERY_KEYS();
  const defaultMinKeys = await geoSafeRecovery.DEFAULT_MIN_KEYS();
  const maxKeys = await geoSafeRecovery.MAX_RECOVERY_KEYS();
  
  console.log(chalk.gray(`   Default recovery delay: ${defaultDelay}s`));
  console.log(chalk.gray(`   Min recovery keys: ${minKeys}`));
  console.log(chalk.gray(`   Default min keys: ${defaultMinKeys}`));
  console.log(chalk.gray(`   Max recovery keys: ${maxKeys}`));

  // Save deployment configuration
  const deploymentConfig = {
    network: await deployer.provider.getNetwork().then(n => n.name),
    contractAddress: contractAddress,
    deployerAddress: deployer.address,
    deploymentBlock: deployTx ? await deployTx.wait().then(r => r?.blockNumber) : null,
    deploymentHash: deployTx?.hash || null,
    timestamp: new Date().toISOString(),
    version: "2.0.0",
    improvements: [
      "Gas optimization with packed structs",
      "Removed unused code and debug functions", 
      "Added comprehensive input validation",
      "Improved reentrancy protection",
      "Better error handling with specific error types",
      "Added safe configuration management",
      "Optimized loops and storage access",
      "Enhanced signature verification"
    ],
    constants: {
      DEFAULT_RECOVERY_DELAY: defaultDelay.toString(),
      MIN_RECOVERY_KEYS: minKeys.toString(),
      DEFAULT_MIN_KEYS: defaultMinKeys.toString(),
      MAX_RECOVERY_KEYS: maxKeys.toString()
    }
  };

  const configPath = path.join(__dirname, '..', 'deployments', 'sepolia-deployment.json');
  fs.writeFileSync(configPath, JSON.stringify(deploymentConfig, null, 2));
  console.log(chalk.green(`\n💾 Deployment config saved to: ${configPath}`));

  // Verify contract on Etherscan
  console.log(chalk.cyan("\n🔍 Verifying contract on Etherscan..."));
  try {
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds for deployment to propagate
    
    await run("verify:verify", {
      address: contractAddress,
      constructorArguments: [deployer.address],
    });
    
    console.log(chalk.green("✅ Contract verified successfully on Etherscan!"));
  } catch (error: any) {
    if (error.message.includes("Already Verified")) {
      console.log(chalk.yellow("⚠️  Contract already verified on Etherscan"));
    } else {
      console.log(chalk.red("❌ Contract verification failed:"));
      console.log(chalk.red(error.message));
      console.log(chalk.yellow("💡 You can verify manually later using:"));
      console.log(chalk.gray(`   npx hardhat verify --network sepolia ${contractAddress} ${deployer.address}`));
    }
  }

  // Summary
  console.log(chalk.blue("\n📋 DEPLOYMENT SUMMARY"));
  console.log(chalk.blue("===================="));
  console.log(chalk.green(`✅ GeoSafeRecovery: ${contractAddress}`));
  console.log(chalk.yellow(`🌐 Network: ${await deployer.provider.getNetwork().then(n => n.name)}`));
  console.log(chalk.yellow(`📱 Explorer: https://sepolia.etherscan.io/address/${contractAddress}`));
  console.log(chalk.yellow(`🔍 Verification: Check explorer link above for verification status`));
  
  console.log(chalk.blue("\n🎯 KEY FEATURES:"));
  console.log(chalk.gray("   • Gas optimized with packed structs"));
  console.log(chalk.gray("   • Removed debug functions for production"));
  console.log(chalk.gray("   • Enhanced input validation & error handling"));
  console.log(chalk.gray("   • Better reentrancy protection"));
  console.log(chalk.gray("   • Optimized storage access patterns"));
  console.log(chalk.gray("   • Added DOS protection with MAX_RECOVERY_KEYS"));
  console.log(chalk.gray("   • Simplified batch actions for better gas usage"));
  console.log(chalk.gray("   • Added safe configuration management"));

  console.log(chalk.green("\n🎉 Deployment completed successfully!"));
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("\n❌ Deployment failed:"));
    console.error(error);
    process.exit(1);
  });