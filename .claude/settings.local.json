{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(npm run compile:*)", "Bash(npx ts-node:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "Bash(SEPOLIA_RPC_URL=\"https://sepolia.gateway.tenderly.co\" PRIVATE_KEY=\"0x0000000000000000000000000000000000000000000000000000000000000001\" timeout 5s npm run test:e2e)", "Bash(npm run test:e2e:*)", "Bash(ts-node:*)", "Bash(npm run deploy:sepolia:*)", "<PERSON><PERSON>(npx hardhat run:*)", "WebFetch(domain:docs.safe.global)", "<PERSON><PERSON>(npx hardhat:*)", "Bash(timeout 120 bash -c 'printf \"\"1\\ny\\n3\\ny\\n\"\" | npx ts-node scripts/run-simulator.ts')", "Bash(npm run:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git commit:*)", "Bash(npm test)"], "deny": []}}