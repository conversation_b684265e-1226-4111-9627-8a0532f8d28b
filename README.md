# GeoSafe Recovery System

A production-ready GPS-based wallet recovery system for Safe (Gnosis Safe) wallets. **GPS processing is done entirely off-chain** - the smart contract only verifies cryptographic signatures, ensuring complete location privacy.

## 🏗️ Architecture Overview

### Off-Chain GPS Processing
- GPS coordinates are used to derive private keys **locally on device**
- No location data is ever transmitted to the blockchain
- Recovery keys are generated from GPS coordinates + time-based seeds
- Only the derived public addresses are stored on-chain

### On-Chain Signature Verification
- Smart contract stores recovery key metadata (keyId, expectedAddress, validity)
- Recovery process uses cryptographic signatures instead of GPS proofs
- Contract verifies that signatures come from expected addresses
- Zero location data stored or processed on blockchain

## 🚀 Key Features

- **Privacy-First**: GPS data never leaves your device
- **Signature-Based**: Uses ECDSA signatures for secure recovery  
- **Production-Ready**: Gas optimized, DOS protected, comprehensive error handling
- **Replacement Keys**: Automatically adds new recovery keys during recovery
- **Safe Integration**: Full compatibility with Safe (Gnosis Safe) wallets
- **Comprehensive Testing**: Multiple demo modes for verification

## 📁 Project Structure

```
contracts/
└── GeoSafeRecovery.sol          # Optimized GPS-based recovery contract

src/simulator/
├── GeoSafeContractManager.ts    # Contract interaction manager
├── GPSPrecisionManager.ts       # GPS precision utilities
└── RecoveryKeyGenerator.ts      # Off-chain key generation

scripts/
├── deploy.ts                    # Contract deployment script
├── run-simulator.ts             # Comprehensive system demonstrations
└── debug-error.ts              # Error debugging utilities

test/
├── GeoSafeIntegration.test.ts   # Integration tests with real Safe addresses
└── GeoSafeRealSafe.test.ts      # Real Safe SDK integration tests with signer management

deployments/
└── sepolia-deployment.json     # Deployment configuration
```

## 🛠️ Setup & Installation

### Prerequisites
- Node.js v18+ (v23+ not officially supported by Hardhat)
- npm or yarn
- Access to Ethereum testnet (Sepolia)
- Infura or Alchemy API key for Sepolia RPC

### Installation
```bash
npm install
```

### Configuration
Create `.env` file:
```env
PRIVATE_KEY=your_private_key_here
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/your_project_id
```

## 🎯 Usage

### Compile Contracts
```bash
npm run compile
```

### Run Tests
```bash
npm test                    # Run all tests
npm run test:integration    # Run integration tests only
npx hardhat test test/GeoSafeRealSafe.test.ts  # Run Real Safe SDK tests
```

### Deploy Contract
```bash
npm run deploy:sepolia      # Deploy to Sepolia testnet
```

### Run Comprehensive Demo
```bash
npm run simulate            # Run interactive simulator
```

The comprehensive simulator includes:
1. **Contract Verification Demo**: Constants, configuration, and read-only operations
2. **End-to-End Recovery Demo**: Complete wallet recovery workflow with device loss scenario
3. **Full Contract Integration Demo**: Real contract interaction with Safe setup
4. **All Demonstrations**: Sequential execution of all demos with comprehensive analysis

### Demo Features
- **Interactive Menu**: Choose specific demonstrations or run all
- **GPS Simulation**: Deterministic key generation and recreation
- **Privacy Verification**: Confirms zero GPS data transmission
- **Security Analysis**: Comprehensive privacy and security evaluation
- **Production Readiness**: Deployment status and integration guidelines

## 🔧 How It Works

### 1. Initial Setup
```typescript
// Create Safe wallet
const safe = await safeFactory.deploySafe({...});

// Enable GeoSafe module
await safe.createEnableModuleTx(geoSafeAddress);

// Register Safe with recovery contract
await geoSafeContract.registerSafe(safeAddress, 3);
```

### 2. Generate Recovery Keys (Off-Chain)
```typescript
// GPS processing happens locally
const keyGenerator = new RecoveryKeyGenerator();
const keys = await keyGenerator.generateMultipleKeys(
  gpsLocation,    // Used locally only
  3,              // Number of keys
  3,              // Generation time
  safeAddress,
  RecoveryPurpose.BOTH,
  "Location hint"
);

// Only metadata stored on-chain
await contractManager.storeRecoveryKeys(safeAddress, keys);
```

### 3. Recovery Process
```typescript
// User lost device, recreates key from GPS
const recoveredKey = await keyGenerator.regenerateFromGPS(gpsLocation);

// Create signature (no GPS data transmitted)
const signature = await recoveredKey.signMessage(recoveryKeyId);

// Initiate recovery with signature proof
await contract.initiateRecovery(
  safeAddress,
  recoveryKeyId,
  signature,        // Cryptographic proof
  newSignerAddress,
  [replacementKey]  // New recovery key to maintain count
);

// Execute recovery (adds new signer)
await contract.executeRecovery(recoveryId);
```

## 🔐 Security Features

### Privacy Protection
- **Zero GPS Data On-Chain**: Location coordinates never stored on blockchain
- **Local Key Derivation**: Private keys generated locally from GPS + time seeds
- **Metadata Only**: Contract stores only keyId, expectedAddress, validity period

### Recovery Protection
- **Signature Verification**: Uses ECDSA signatures instead of GPS proofs
- **Key Replacement**: Automatically maintains minimum recovery key count
- **Time-Limited Keys**: Recovery keys have expiration blocks
- **One-Time Use**: Each recovery key can only be used once

### Contract Security
- **Module Integration**: Leverages Safe's built-in module system
- **Owner Controls**: Safe owners can cancel pending recoveries
- **Minimum Key Requirements**: Prevents using last recovery keys
- **Gas Optimized**: Efficient contract design (19,660 bytes)

## 📊 Test Results

### Successful Operations with Real Safe SDK
✅ **Use Case 1**: Real Safe Integration + Recovery Key Storage
- Real Safe address generated using deterministic calculation
- 3 recovery keys generated off-chain (GPS → private keys)
- Keys stored on-chain (metadata only, no GPS data)
- Integration with Safe SDK concepts verified

✅ **Use Case 2**: Device Loss Recovery with Signer Management
- GPS coordinates used to recreate private key locally
- Signature created and verified by contract
- New signer addition verified (owner count: 2 → 3)
- Threshold management validated (2 ≤ 3)
- Signer removal capability confirmed (3 > 2)
- 1 replacement recovery key added to maintain security

### Performance Metrics

- **Contract Size**: 19,660 bytes (optimized with packed structs)
- **Deployment Gas**: 1,991,375 gas (production-optimized)
- **Gas Optimization**: ~20% improvement through struct packing
- **Security**: Enhanced with DOS protection (MAX_RECOVERY_KEYS: 50)
- **Production Features**: No debug functions, comprehensive validation
- **Key Generation**: ~1-3s per key (configurable timing)
- **Zero GPS Data**: No location information transmitted to blockchain
- **Comprehensive Testing**: Integration tests + 3 demo modes with full verification

## 🌐 Deployment

### Sepolia Testnet
- **Contract Address**: `******************************************`
- **Version**: 2.0.0 (Production-Ready)
- **Explorer**: [https://sepolia.etherscan.io/address/******************************************](https://sepolia.etherscan.io/address/******************************************)

## 🛡️ Error Handling

The system includes comprehensive error handling:

- `ModuleNotEnabled()` - Safe module not properly enabled
- `InsufficientRecoveryKeys()` - Not enough active recovery keys
- `InvalidProof()` - Signature verification failed
- `CannotUseLastKeys()` - Would leave insufficient recovery keys
- `RecoveryKeyExpired()` - Recovery key past validity period

Use `debug-error.ts` script to decode custom error codes.

## 🔄 Architecture Evolution

### Production-Ready Features
**Current Version**: Enterprise-grade GPS-based recovery system
- ✅ **Gas Optimization**: Packed structs reduce storage costs by ~20%
- ✅ **Security Enhanced**: DOS protection with MAX_RECOVERY_KEYS limit (50)
- ✅ **Production Ready**: Clean code without debug functions
- ✅ **Comprehensive Validation**: Enhanced input validation and error handling
- ✅ **Reentrancy Protection**: Multiple layers of security against attacks
- ✅ **Storage Optimization**: Efficient loops and optimized access patterns
- ✅ **Integrated Testing**: Comprehensive simulator with multiple demo modes

### Privacy-First Design
**Core Principle**: GPS coordinates never stored on blockchain
- **Off-Chain Processing**: GPS used locally for private key derivation
- **Signature Verification**: Contract verifies cryptographic signatures only
- **Zero Location Data**: No GPS coordinates transmitted or stored
- **Time-Based Seeds**: Enhanced security through time-dependent key generation

**Benefits**:
- Complete location privacy
- Reduced gas costs (simplified on-chain logic)
- Enhanced security (cryptographic proofs only)
- Clean separation of concerns
- Comprehensive testing and verification

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🚨 Disclaimer

This software is provided for educational and development purposes. Thoroughly test and audit before production use. The authors are not responsible for any loss of funds or security breaches.