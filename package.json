{"name": "geosafe", "version": "1.0.0", "description": "GeoSafe - Secure wallet recovery system for Safe wallets", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:integration": "hardhat test --grep \"GeoSafe Integration Tests\"", "deploy:sepolia": "hardhat run scripts/deploy.ts --network sepolia", "simulate": "hardhat run scripts/run-simulator.ts --network sepolia", "verify": "hardhat verify --network sepolia", "debug": "ts-node scripts/debug-error.ts"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.1.0", "@nomicfoundation/hardhat-ethers": "^3.1.0", "@nomicfoundation/hardhat-network-helpers": "^1.1.0", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "@nomicfoundation/hardhat-verify": "^1.1.1", "@openzeppelin/contracts": "^5.0.0", "@typechain/ethers-v6": "^0.4.3", "@typechain/hardhat": "^8.0.3", "@types/chai": "^4.3.11", "@types/inquirer": "^8.2.10", "@types/mocha": "^10.0.6", "@types/node": "^20.10.5", "chai": "^4.3.10", "hardhat": "^2.19.2", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.5", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.3.3"}, "dependencies": {"@safe-global/api-kit": "^2.4.1", "@safe-global/protocol-kit": "^4.0.2", "@safe-global/safe-contracts": "^1.4.1-2", "@safe-global/types-kit": "^1.0.0", "chalk": "^4.1.2", "dotenv": "^16.3.1", "ethers": "^6.9.0", "inquirer": "^8.2.6"}}