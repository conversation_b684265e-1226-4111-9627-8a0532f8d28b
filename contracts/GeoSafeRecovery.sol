// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// Safe interface definitions
interface ISafe {
    function isOwner(address owner) external view returns (bool);
    function getThreshold() external view returns (uint256);
    function isModuleEnabled(address module) external view returns (bool);
    function execTransactionFromModule(
        address to,
        uint256 value,
        bytes memory data,
        uint8 operation
    ) external returns (bool success);
}

/**
 * @title GeoSafeRecovery
 * @dev Optimized GPS-based recovery system for Safe wallets
 * @notice GPS processing done off-chain, contract only verifies signatures
 */
contract GeoSafeRecovery {
    // ========== Constants ==========

    uint256 public constant DEFAULT_RECOVERY_DELAY = 0;
    uint256 public constant MIN_RECOVERY_KEYS = 2;
    uint256 public constant DEFAULT_MIN_KEYS = 3;
    uint256 public constant MAX_RECOVERY_KEYS = 50; // Prevent DOS attacks
    uint8 public constant CALL = 0;

    // ========== State Variables ==========

    // Packed struct for gas optimization
    struct RecoveryKey {
        bytes32 keyId; // 32 bytes
        address safe; // 20 bytes
        address expectedAddress; // 20 bytes
        uint96 validUntilBlock; // 12 bytes (enough for block numbers)
        bool isUsed; // 1 byte
        RecoveryPurpose purpose; // 1 byte
        string hint; // dynamic
        uint96 stretchedCount; // 12 bytes - track stretched hash
    }

    enum RecoveryPurpose {
        ADD_SIGNER,
        ADD_RECOVERY_KEYS,
        BOTH
    }

    // Optimized SafeConfig - packed into single slot
    struct SafeConfig {
        bool isRegistered; // 1 byte
        bool pauseRecovery; // 1 byte
        uint96 customRecoveryDelay; // 12 bytes
        uint96 minKeysRequired; // 12 bytes
        uint32 totalKeys; // 4 bytes - track total for gas optimization
    }

    struct PendingRecovery {
        address safe;
        bytes32 recoveryKeyId;
        bytes32 recoveryId; // Unique ID for recovery
        address initiator;
        address newSigner;
        uint256 executionTime;
        bool executed;
        bool cancelled;
        RecoveryKey[] newKeys; // Simplified - direct storage
    }

    // Storage mappings
    mapping(address => bytes32[]) public safeRecoveryKeys;
    mapping(bytes32 => RecoveryKey) public recoveryKeys;
    mapping(bytes32 => PendingRecovery) public pendingRecoveries;
    mapping(address => SafeConfig) public safeConfigs;

    address public immutable geoSafeFactory;

    // ========== Events ==========

    event SafeRegistered(address indexed safe, uint256 minKeys);
    event RecoveryKeysAdded(address indexed safe, bytes32[] keyIds, uint256 totalKeys);
    event RecoveryInitiated(address indexed safe, bytes32 indexed recoveryKeyId, bytes32 keyId);
    event RecoveryExecuted(address indexed safe, bytes32 indexed recoveryKeyId, address newSigner);
    event RecoveryCancelled(address indexed safe, bytes32 indexed recoveryKeyId);
    event RecoveryKeyUsed(address indexed safe, bytes32 indexed keyId);
    event SafeConfigUpdated(address indexed safe, uint256 minKeys, bool paused);

    // ========== Errors ==========

    error Unauthorized();
    error SafeNotRegistered();
    error SafeAlreadyRegistered();
    error ModuleNotEnabled();
    error InsufficientRecoveryKeys(uint256 current, uint256 required);
    error RecoveryKeyNotFound();
    error RecoveryKeyAlreadyUsed();
    error RecoveryKeyExpired();
    error InvalidSignature();
    error RecoveryNotFound();
    error RecoveryAlreadyExecuted();
    error RecoveryDelayNotPassed();
    error RecoveryPaused();
    error ActionExecutionFailed();
    error CannotUseLastKeys();
    error TooManyKeys();
    error InvalidAddress();
    error InvalidKeyId();
    error DuplicateKeyId();
    error RecoveryAlreadyCancelled();

    // ========== Modifiers ==========

    modifier onlyRegisteredSafe(address safe) {
        if (!safeConfigs[safe].isRegistered) revert SafeNotRegistered();
        _;
    }

    modifier onlySafeOwner(address safe, bytes calldata signature) {
        if (signature.length > 0) {
            // If signature is provided, verify it instead of checking msg.sender
            bytes32 messageHash = keccak256(
                abi.encodePacked(
                    "\x19Ethereum Signed Message:\n32",
                    keccak256(abi.encodePacked(safe))
                )
            );
            address recoveredAddress = _recoverSigner(messageHash, signature);
            if (recoveredAddress == address(0) || !ISafe(safe).isOwner(recoveredAddress)) {
                revert Unauthorized();
            }
        } else {
            // If no signature provided, check if msg.sender is owner
            if (!ISafe(safe).isOwner(msg.sender)) revert Unauthorized();
        }
        _;
    }

    modifier recoveryNotPaused(address safe) {
        if (safeConfigs[safe].pauseRecovery) revert RecoveryPaused();
        _;
    }

    modifier validAddress(address addr) {
        if (addr == address(0)) revert InvalidAddress();
        _;
    }

    // ========== Constructor ==========

    constructor(address _geoSafeFactory) validAddress(_geoSafeFactory) {
        geoSafeFactory = _geoSafeFactory;
    }

    // ========== External Functions ==========

    /**
     * @dev Register a new Safe wallet with GPS recovery
     */
    function registerSafe(
        address safe,
        uint256 minKeysRequired,
        bytes calldata signature
    ) external validAddress(safe) onlySafeOwner(safe, signature) {

        if (safeConfigs[safe].isRegistered) revert SafeAlreadyRegistered();

        // Check module enablement
        if (!ISafe(safe).isModuleEnabled(address(this))) {
            revert ModuleNotEnabled();
        }

        uint96 minKeys = minKeysRequired == 0 ? uint96(DEFAULT_MIN_KEYS) : uint96(minKeysRequired);

        safeConfigs[safe] = SafeConfig({
            isRegistered: true,
            customRecoveryDelay: 0,
            pauseRecovery: false,
            minKeysRequired: minKeys,
            totalKeys: 0
        });

        emit SafeRegistered(safe, minKeys);
    }

    /**
     * @dev Add multiple recovery keys to a Safe (optimized)
     */
    function addRecoveryKeys(
        address safe,
        RecoveryKey[] calldata keys,
        bytes calldata signature
    ) external onlyRegisteredSafe(safe) onlySafeOwner(safe, signature) {
        uint256 keysLength = keys.length;
        if (keysLength == 0) return;

        SafeConfig storage config = safeConfigs[safe];
        if (config.totalKeys + keysLength > MAX_RECOVERY_KEYS) {
            revert TooManyKeys();
        }

        bytes32[] memory keyIds = new bytes32[](keysLength);

        // Batch validation and storage
        for (uint256 i = 0; i < keysLength; i++) {
            RecoveryKey calldata key = keys[i];

            // Validate key
            if (key.keyId == bytes32(0)) revert InvalidKeyId();
            if (key.expectedAddress == address(0)) revert InvalidAddress();
            if (key.safe != safe) revert Unauthorized();
            if (recoveryKeys[key.keyId].keyId != bytes32(0)) revert DuplicateKeyId();

            // Store recovery key
            recoveryKeys[key.keyId] = key;
            safeRecoveryKeys[safe].push(key.keyId);
            keyIds[i] = key.keyId;
        }

        // Update total count
        config.totalKeys += uint32(keysLength);

        emit RecoveryKeysAdded(safe, keyIds, config.totalKeys);
    }

    /**
     * @dev Initiate recovery using signature verification
     */
    function initiateRecovery(
        address safe,
        bytes32 recoveryKeyId,
        bytes calldata signature,
        address newSigner,
        RecoveryKey[] calldata newKeys
    ) external onlyRegisteredSafe(safe) recoveryNotPaused(safe) validAddress(newSigner) {
        RecoveryKey storage key = recoveryKeys[recoveryKeyId];

        // Validate recovery key
        if (key.keyId == bytes32(0)) revert RecoveryKeyNotFound();
        if (key.isUsed) revert RecoveryKeyAlreadyUsed();
        if (block.number > key.validUntilBlock) revert RecoveryKeyExpired();
        if (key.safe != safe) revert Unauthorized();

        bytes32 messageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", recoveryKeyId)
        );
        // Verify signature
        address signer = _recoverSigner(messageHash, signature);
        if (signer == address(0) || signer != key.expectedAddress) revert InvalidSignature();

        // Check minimum keys constraint (optimized)
        SafeConfig storage config = safeConfigs[safe];
        uint256 activeKeys = _getActiveKeyCountOptimized(safe);
        uint256 minRequired = config.minKeysRequired == 0
            ? MIN_RECOVERY_KEYS
            : config.minKeysRequired;

        if (activeKeys <= minRequired && newKeys.length == 0) {
            revert CannotUseLastKeys();
        }

        // Mark key as used immediately to prevent reentrancy
        key.isUsed = true;

        // Create unique recovery ID
        bytes32 recoveryId = keccak256(
            abi.encodePacked(safe, recoveryKeyId, block.timestamp, msg.sender, newSigner)
        );

        uint256 delay = config.customRecoveryDelay > 0
            ? config.customRecoveryDelay
            : DEFAULT_RECOVERY_DELAY;

        // Store pending recovery with optimized structure
        PendingRecovery storage recovery = pendingRecoveries[recoveryKeyId];
        recovery.safe = safe;
        recovery.recoveryKeyId = recoveryKeyId;
        recovery.recoveryId = recoveryId;
        recovery.initiator = msg.sender;
        recovery.newSigner = newSigner;
        recovery.executionTime = block.timestamp + delay;
        recovery.executed = false;
        recovery.cancelled = false;

        // Store new keys if provided
        if (newKeys.length > 0) {
            for (uint256 i = 0; i < newKeys.length; i++) {
                recovery.newKeys.push(newKeys[i]);
            }
        }

        emit RecoveryInitiated(safe, recoveryId, recoveryKeyId);
        emit RecoveryKeyUsed(safe, recoveryKeyId);
    }

    /**
     * @dev Execute a pending recovery (optimized)
     */
    function executeRecovery(bytes32 recoveryKeyId) external {
        PendingRecovery storage recovery = pendingRecoveries[recoveryKeyId];

        if (recovery.safe == address(0)) revert RecoveryNotFound();
        if (recovery.executed) revert RecoveryAlreadyExecuted();
        if (recovery.cancelled) revert RecoveryAlreadyCancelled();
        if (block.timestamp < recovery.executionTime) revert RecoveryDelayNotPassed();
        if (safeConfigs[recovery.safe].pauseRecovery) revert RecoveryPaused();

        // Mark as executed first to prevent reentrancy
        recovery.executed = true;

        // Add new signer to Safe
        _addSignerToSafe(recovery.safe, recovery.newSigner);

        // Add new recovery keys if any
        if (recovery.newKeys.length > 0) {
            _addRecoveryKeysInternal(recovery.safe, recovery.newKeys);
        }

        emit RecoveryExecuted(recovery.safe, recoveryKeyId, recovery.newSigner);
    }

    /**
     * @dev Cancel a pending recovery
     */
    function cancelRecovery(bytes32 recoveryKeyId, bytes calldata signature) external {
        PendingRecovery storage recovery = pendingRecoveries[recoveryKeyId];

        if (recovery.safe == address(0)) revert RecoveryNotFound();
        if (recovery.executed) revert RecoveryAlreadyExecuted();

        // Use signature-based authentication
        if (signature.length > 0) {
            bytes32 messageHash = keccak256(
                abi.encodePacked(
                    "\x19Ethereum Signed Message:\n32",
                    keccak256(abi.encodePacked(recovery.safe, msg.sender, block.timestamp))
                )
            );
            address recoveredAddress = _recoverSigner(messageHash, signature);
            if (recoveredAddress == address(0) || !ISafe(recovery.safe).isOwner(recoveredAddress)) {
                revert Unauthorized();
            }
        } else {
            if (!ISafe(recovery.safe).isOwner(msg.sender)) revert Unauthorized();
        }

        recovery.cancelled = true;
        emit RecoveryCancelled(recovery.safe, recoveryKeyId);
    }

    /**
     * @dev Update Safe configuration (new function)
     */
    function updateSafeConfig(
        address safe,
        uint256 newMinKeys,
        bool pauseRecovery,
        bytes calldata signature
    ) external onlyRegisteredSafe(safe) onlySafeOwner(safe, signature) {
        SafeConfig storage config = safeConfigs[safe];
        config.minKeysRequired = uint96(newMinKeys);
        config.pauseRecovery = pauseRecovery;

        emit SafeConfigUpdated(safe, newMinKeys, pauseRecovery);
    }

    // ========== View Functions (Optimized) ==========

    /**
     * @dev Get recovery keys for a Safe
     */
    function getRecoveryKeys(address safe) external view returns (bytes32[] memory) {
        return safeRecoveryKeys[safe];
    }

    /**
     * @dev Get available recovery keys (gas optimized)
     */
    function getAvailableRecoveryKeys(address safe) external view returns (bytes32[] memory) {
        bytes32[] memory availableKeys = safeRecoveryKeys[safe];
        return availableKeys;
    }

    /**
     * @dev Get active key count (optimized with caching)
     */
    function getActiveKeyCount(address safe) external view returns (uint256) {
        return _getActiveKeyCountOptimized(safe);
    }

    /**
     * @dev Check if recovery key can be used
     */
    function canUseRecoveryKey(address safe, bytes32 keyId) external view returns (bool) {
        RecoveryKey storage key = recoveryKeys[keyId];

        if (key.keyId == bytes32(0) || key.safe != safe) return false;
        if (!_isKeyActive(keyId)) return false;

        uint256 activeKeys = _getActiveKeyCountOptimized(safe);
        uint256 minRequired = safeConfigs[safe].minKeysRequired;
        if (minRequired == 0) minRequired = MIN_RECOVERY_KEYS;

        return activeKeys > minRequired;
    }

    /**
     * @dev Get recovery key details
     */
    function getRecoveryKeyDetails(bytes32 keyId) external view returns (RecoveryKey memory) {
        return recoveryKeys[keyId];
    }

    /**
     * @dev Get Safe configuration
     */
    function getSafeConfig(address safe) external view returns (SafeConfig memory) {
        return safeConfigs[safe];
    }

    // ========== Internal Functions (Optimized) ==========

    /**
     * @dev Check if key is active (helper function)
     */
    function _isKeyActive(bytes32 keyId) internal view returns (bool) {
        RecoveryKey storage key = recoveryKeys[keyId];
        return !key.isUsed && block.number <= key.validUntilBlock;
    }

    /**
     * @dev Optimized active key count using safe config cache
     */
    function _getActiveKeyCountOptimized(address safe) internal view returns (uint256) {
        bytes32[] memory allKeys = safeRecoveryKeys[safe];
        uint256 activeCount = 0;
        uint256 length = allKeys.length;

        for (uint256 i = 0; i < length; i++) {
            if (_isKeyActive(allKeys[i])) {
                activeCount++;
            }
        }

        return activeCount;
    }

    /**
     * @dev Add new signer to Safe
     */
    function _addSignerToSafe(address safe, address signer) internal {
        uint256 threshold = ISafe(safe).getThreshold();

        bytes memory data = abi.encodeWithSignature(
            "addOwnerWithThreshold(address,uint256)",
            signer,
            threshold
        );

        bool success = ISafe(safe).execTransactionFromModule(safe, 0, data, CALL);

        if (!success) revert ActionExecutionFailed();
    }

    /**
     * @dev Internal function to add recovery keys
     */
    function _addRecoveryKeysInternal(address safe, RecoveryKey[] memory keys) internal {
        uint256 keysLength = keys.length;
        bytes32[] memory keyIds = new bytes32[](keysLength);

        for (uint256 i = 0; i < keysLength; i++) {
            recoveryKeys[keys[i].keyId] = keys[i];
            safeRecoveryKeys[safe].push(keys[i].keyId);
            keyIds[i] = keys[i].keyId;
        }

        // Update total count
        safeConfigs[safe].totalKeys += uint32(keysLength);

        emit RecoveryKeysAdded(safe, keyIds, safeConfigs[safe].totalKeys);
    }

    /**
     * @dev Recover signer from message hash and signature
     * @param messageHash The hash of the message that was signed
     * @param signature The signature bytes (65 bytes: r + s + v)
     * @return The address that signed the message
     */
    function _recoverSigner(
        bytes32 messageHash,
        bytes memory signature
    ) internal pure returns (address) {
        if (signature.length != 65) return address(0);

        bytes32 r;
        bytes32 s;
        uint8 v;

        assembly {
            r := mload(add(signature, 32))
            s := mload(add(signature, 64))
            v := byte(0, mload(add(signature, 96)))
        }

        // Adjust v if needed (some wallets use 0/1 instead of 27/28)
        if (v < 27) v += 27;

        // Validate signature parameters
        if (v != 27 && v != 28) return address(0);

        // Recover the signer address
        address signer = ecrecover(messageHash, v, r, s);
        return signer;
    }
}
