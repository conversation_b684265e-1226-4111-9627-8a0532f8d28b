import chalk from "chalk";

/**
 * GPS location with precision information
 */
export interface GPSLocation {
  latitude: number;    // Decimal degrees
  longitude: number;   // Decimal degrees
  precision: number;   // Accuracy in meters
  timestamp: number;   // When the reading was taken
}

/**
 * GPS reading with metadata
 */
export interface GPSReading {
  location: GPSLocation;
  source: 'device' | 'manual' | 'simulator';
  reliability: number; // 0-1 scale
}

/**
 * GPS precision manager for GeoSafe recovery system
 * Handles GPS precision requirements (<10m) and location validation
 */
export class GPSPrecisionManager {
  private static readonly TARGET_PRECISION = 10; // meters
  private static readonly MAX_WAIT_TIME = 300000; // 5 minutes
  private static readonly READING_INTERVAL = 1000; // 1 second
  private static readonly MIN_READINGS = 3; // Minimum readings for averaging

  private currentReadings: GPSReading[] = [];
  private isCollecting = false;

  /**
   * Wait for GPS precision to reach target accuracy
   */
  async waitForPrecision(
    targetPrecision: number = GPSPrecisionManager.TARGET_PRECISION,
    maxWaitMs: number = GPSPrecisionManager.MAX_WAIT_TIME
  ): Promise<GPSLocation> {
    console.log(chalk.yellow(`📍 Waiting for GPS precision <${targetPrecision}m...`));
    
    const startTime = Date.now();
    this.isCollecting = true;
    this.currentReadings = [];

    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(async () => {
        try {
          // Simulate GPS reading (in production, use real GPS API)
          const reading = await this.simulateGPSReading();
          this.currentReadings.push(reading);

          console.log(chalk.gray(
            `GPS: ${reading.location.latitude.toFixed(6)}, ${reading.location.longitude.toFixed(6)} ` +
            `(±${reading.location.precision}m)`
          ));

          // Check if we have sufficient precision
          if (reading.location.precision <= targetPrecision) {
            console.log(chalk.green(`✅ GPS precision achieved: ±${reading.location.precision}m`));
            
            // Average multiple readings for better accuracy
            const avgLocation = this.averageReadings();
            
            clearInterval(checkInterval);
            this.isCollecting = false;
            resolve(avgLocation);
            return;
          }

          // Check timeout
          if (Date.now() - startTime > maxWaitMs) {
            clearInterval(checkInterval);
            this.isCollecting = false;
            reject(new Error(`GPS precision timeout: Unable to achieve <${targetPrecision}m in ${maxWaitMs/1000}s`));
            return;
          }

        } catch (error) {
          clearInterval(checkInterval);
          this.isCollecting = false;
          reject(error);
        }
      }, GPSPrecisionManager.READING_INTERVAL);
    });
  }

  /**
   * Get current GPS precision
   */
  async getCurrentPrecision(): Promise<number> {
    const reading = await this.simulateGPSReading();
    return reading.location.precision;
  }

  /**
   * Validate that current location matches expected location within tolerance
   */
  async validateLocation(expected: GPSLocation, tolerance: number = 10): Promise<boolean> {
    console.log(chalk.yellow(`🔍 Validating location within ${tolerance}m...`));
    
    const current = await this.waitForPrecision(tolerance);
    const distance = this.calculateDistance(expected, current);
    
    console.log(chalk.gray(`Distance from expected: ${distance.toFixed(1)}m`));
    
    const isValid = distance <= tolerance;
    console.log(isValid ? 
      chalk.green(`✅ Location validated (${distance.toFixed(1)}m within ${tolerance}m)`) :
      chalk.red(`❌ Location validation failed (${distance.toFixed(1)}m > ${tolerance}m)`)
    );
    
    return isValid;
  }

  /**
   * Calculate distance between two GPS locations using Haversine formula
   */
  calculateDistance(loc1: GPSLocation, loc2: GPSLocation): number {
    const R = 6371000; // Earth's radius in meters
    const φ1 = loc1.latitude * Math.PI / 180;
    const φ2 = loc2.latitude * Math.PI / 180;
    const Δφ = (loc2.latitude - loc1.latitude) * Math.PI / 180;
    const Δλ = (loc2.longitude - loc1.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Get scaled GPS coordinates for smart contract storage
   * Scales to 6 decimal places (e.g., 37.7749 -> 37774900)
   */
  scaleCoordinates(location: GPSLocation): { lat: number; lng: number } {
    return {
      lat: Math.round(location.latitude * 1000000),
      lng: Math.round(location.longitude * 1000000)
    };
  }

  /**
   * Convert scaled coordinates back to decimal degrees
   */
  unscaleCoordinates(scaledLat: number, scaledLng: number): GPSLocation {
    return {
      latitude: scaledLat / 1000000,
      longitude: scaledLng / 1000000,
      precision: 0, // Unknown precision for unscaled coordinates
      timestamp: Date.now()
    };
  }

  /**
   * Generate location fingerprint for key derivation
   */
  generateLocationFingerprint(location: GPSLocation, precision: number = 10): string {
    // Round to precision grid to ensure consistent fingerprints
    const gridLat = Math.round(location.latitude * 1000000 / precision) * precision;
    const gridLng = Math.round(location.longitude * 1000000 / precision) * precision;
    
    return `${gridLat},${gridLng}`;
  }

  /**
   * Simulate GPS reading for testing
   * In production, replace with actual GPS API calls
   */
  private async simulateGPSReading(): Promise<GPSReading> {
    // Simulate GPS accuracy improving over time
    const baseAccuracy = 15; // Start with 15m accuracy
    const improvementRate = 0.95; // Improve by 5% each reading
    const readingCount = this.currentReadings.length;
    
    const precision = Math.max(
      GPSPrecisionManager.TARGET_PRECISION - 2, // Best case: 8m
      baseAccuracy * Math.pow(improvementRate, readingCount)
    );

    // Add some randomness to simulate real GPS
    const latNoise = (Math.random() - 0.5) * 0.0001; // ~10m noise
    const lngNoise = (Math.random() - 0.5) * 0.0001;

    return {
      location: {
        latitude: 37.7749 + latNoise,    // San Francisco
        longitude: -122.4194 + lngNoise,
        precision: precision,
        timestamp: Date.now()
      },
      source: 'simulator',
      reliability: Math.min(0.9, 0.5 + (readingCount * 0.1))
    };
  }

  /**
   * Average multiple GPS readings for better accuracy
   */
  private averageReadings(): GPSLocation {
    if (this.currentReadings.length === 0) {
      throw new Error('No GPS readings available for averaging');
    }

    // Use only the most recent readings with good precision
    const goodReadings = this.currentReadings
      .filter(r => r.location.precision <= GPSPrecisionManager.TARGET_PRECISION)
      .slice(-GPSPrecisionManager.MIN_READINGS);

    if (goodReadings.length === 0) {
      return this.currentReadings[this.currentReadings.length - 1].location;
    }

    const avgLat = goodReadings.reduce((sum, r) => sum + r.location.latitude, 0) / goodReadings.length;
    const avgLng = goodReadings.reduce((sum, r) => sum + r.location.longitude, 0) / goodReadings.length;
    const bestPrecision = Math.min(...goodReadings.map(r => r.location.precision));

    return {
      latitude: avgLat,
      longitude: avgLng,
      precision: bestPrecision,
      timestamp: Date.now()
    };
  }

  /**
   * Check if GPS is currently collecting readings
   */
  isGPSActive(): boolean {
    return this.isCollecting;
  }

  /**
   * Get all current GPS readings
   */
  getAllReadings(): GPSReading[] {
    return [...this.currentReadings];
  }

  /**
   * Clear all GPS readings
   */
  clearReadings(): void {
    this.currentReadings = [];
  }
}