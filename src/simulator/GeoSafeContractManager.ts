import { ethers } from 'ethers';
import chalk from 'chalk';
import { <PERSON>K<PERSON>, GPSRecoveryKey, RecoveryPurpose } from './RecoveryKeyGenerator';

/**
 * Real contract manager for GeoSafe interactions
 * Uses deployed GeoSafeRecovery contract instead of mock data
 */
export class GeoSafeContractManager {
  private contract: ethers.Contract;
  private signer: ethers.Signer;
  private contractAddress: string;

  // Contract ABI for optimized GeoSafeRecovery (no GPS processing on-chain)
  private static readonly ABI = [
    "function registerSafe(address safe, uint256 minKeysRequired) external",
    "function addRecoveryKeys(address safe, tuple(bytes32 keyId, address safe, address expectedAddress, uint96 validUntilBlock, bool isUsed, uint8 purpose, string hint)[] calldata keys) external",
    "function getRecoveryKeys(address safe) external view returns (bytes32[] memory)",
    "function getRecoveryKeyDetails(bytes32 keyId) external view returns (tuple(bytes32 keyId, address safe, address expectedAddress, uint96 validUntilBlock, bool isUsed, uint8 purpose, string hint))",
    "function initiateRecovery(address safe, bytes32 recoveryKeyId, bytes calldata signature, address newSigner, tuple(bytes32 keyId, address safe, address expectedAddress, uint96 validUntilBlock, bool isUsed, uint8 purpose, string hint)[] calldata newKeys) external",
    "function executeRecovery(bytes32 recoveryId) external",
    "function canUseRecoveryKey(address safe, bytes32 keyId) external view returns (bool)",
    "function getActiveKeyCount(address safe) external view returns (uint256)",
    "function getSafeConfig(address safe) external view returns (tuple(bool isRegistered, bool pauseRecovery, uint96 customRecoveryDelay, uint96 minKeysRequired, uint32 totalKeys))",
    "event SafeRegistered(address indexed safe, uint256 minKeys)",
    "event RecoveryKeysAdded(address indexed safe, bytes32[] keyIds, uint256 totalKeys)",
    "event RecoveryInitiated(address indexed safe, bytes32 indexed recoveryId, bytes32 keyId)",
    "event RecoveryExecuted(address indexed safe, bytes32 indexed recoveryId, address newSigner)"
  ];

  constructor(contractAddress: string, signer: ethers.Signer) {
    this.contractAddress = contractAddress;
    this.signer = signer;
    this.contract = new ethers.Contract(contractAddress, GeoSafeContractManager.ABI, signer);
  }

  /**
   * Register Safe with GeoSafe contract
   */
  async registerSafe(safeAddress: string, minimumKeys: number = 3): Promise<void> {
    console.log(chalk.yellow(`📝 Registering Safe ${safeAddress} with contract...`));
    
    try {
      const tx = await this.contract.registerSafe(safeAddress, minimumKeys);
      console.log(chalk.gray(`   Transaction: ${tx.hash}`));
      
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        console.log(chalk.green(`✅ Safe registered successfully`));
        console.log(chalk.gray(`   Block: ${receipt.blockNumber}`));
        console.log(chalk.gray(`   Gas used: ${receipt.gasUsed.toString()}`));
      } else {
        throw new Error('Transaction failed');
      }
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to register Safe: ${error.message}`));
      throw error;
    }
  }

  /**
   * Store recovery keys in contract
   */
  async storeRecoveryKeys(safeAddress: string, keys: GPSRecoveryKey[]): Promise<void> {
    console.log(chalk.yellow(`💾 Storing ${keys.length} recovery keys for Safe ${safeAddress}...`));
    
    try {
      // Convert keys to contract format (simplified - no GPS data on-chain)
      const contractKeys = keys.map(key => ({
        keyId: key.keyId,
        safe: key.safe,
        expectedAddress: key.expectedAddress,
        validUntilBlock: key.validUntilBlock,
        isUsed: key.isUsed,
        purpose: this.purposeToInt(key.purpose),
        hint: key.hint
      }));

      const tx = await this.contract.addRecoveryKeys(safeAddress, contractKeys);
      console.log(chalk.gray(`   Transaction: ${tx.hash}`));
      
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        console.log(chalk.green(`✅ Recovery keys stored successfully`));
        console.log(chalk.gray(`   Block: ${receipt.blockNumber}`));
        console.log(chalk.gray(`   Gas used: ${receipt.gasUsed.toString()}`));
      } else {
        throw new Error('Transaction failed');
      }
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to store recovery keys: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get available recovery keys for a Safe
   */
  async getRecoveryKeys(safeAddress: string): Promise<string[]> {
    console.log(chalk.yellow(`🔍 Getting recovery keys for Safe ${safeAddress}...`));
    
    try {
      const keyIds = await this.contract.getRecoveryKeys(safeAddress);
      console.log(chalk.green(`✅ Found ${keyIds.length} recovery keys`));
      return keyIds;
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to get recovery keys: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get recovery key data from contract
   */
  async getRecoveryKeyData(keyId: string): Promise<RecoveryKey> {
    console.log(chalk.yellow(`🔍 Getting data for recovery key ${keyId.substring(0, 10)}...`));
    
    try {
      const keyData = await this.contract.getRecoveryKeyDetails(keyId);
      
      // Convert from contract format back to RecoveryKey
      const result: RecoveryKey = {
        keyId: keyData.keyId,
        safe: keyData.safe,
        expectedAddress: keyData.expectedAddress,
        validUntilBlock: Number(keyData.validUntilBlock),
        isUsed: keyData.isUsed,
        purpose: this.intToPurpose(keyData.purpose),
        hint: keyData.hint,
        stretchedCount: keyData.stretchedCount || 10000 // Default to 10,000 if not set
      };

      console.log(chalk.green(`✅ Retrieved key data`));
      return result;
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to get recovery key data: ${error.message}`));
      throw error;
    }
  }

  /**
   * Initiate recovery process
   */
  async initiateRecovery(params: {
    safeAddress: string;
    keyId: string;
    signature: string;
    newSignerAddress: string;
    newRecoveryKeys?: GPSRecoveryKey[];
  }): Promise<string> {
    console.log(chalk.yellow(`🚀 Initiating recovery for Safe ${params.safeAddress}...`));
    
    try {
      // Convert new recovery keys to contract format if provided (simplified)
      const contractKeys = (params.newRecoveryKeys || []).map(key => ({
        keyId: key.keyId,
        safe: key.safe,
        expectedAddress: key.expectedAddress,
        validUntilBlock: key.validUntilBlock,
        isUsed: key.isUsed,
        purpose: this.purposeToInt(key.purpose),
        hint: key.hint
      }));

      const tx = await this.contract.initiateRecovery(
        params.safeAddress,
        params.keyId,
        params.signature,
        params.newSignerAddress,
        contractKeys
      );
      
      console.log(chalk.gray(`   Transaction: ${tx.hash}`));
      
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        // Extract recovery ID from event logs
        const recoveryEvent = receipt.logs.find((log: any) => 
          log.topics[0] === this.contract.interface.getEvent('RecoveryInitiated')?.topicHash
        );
        
        let recoveryId: string;
        if (recoveryEvent) {
          const decoded = this.contract.interface.parseLog(recoveryEvent);
          recoveryId = decoded?.args.recoveryId || ethers.keccak256(
            ethers.solidityPacked(
              ['address', 'bytes32', 'uint256'],
              [params.safeAddress, params.keyId, receipt.blockNumber]
            )
          );
        } else {
          // Fallback: generate deterministic ID
          recoveryId = ethers.keccak256(
            ethers.solidityPacked(
              ['address', 'bytes32', 'uint256'],
              [params.safeAddress, params.keyId, receipt.blockNumber]
            )
          );
        }

        console.log(chalk.green(`✅ Recovery initiated successfully`));
        console.log(chalk.gray(`   Recovery ID: ${recoveryId}`));
        console.log(chalk.gray(`   Block: ${receipt.blockNumber}`));
        console.log(chalk.gray(`   Gas used: ${receipt.gasUsed.toString()}`));
        
        return recoveryId;
      } else {
        throw new Error('Transaction failed');
      }
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to initiate recovery: ${error.message}`));
      throw error;
    }
  }

  /**
   * Execute recovery (if delay has passed)
   */
  async executeRecovery(recoveryId: string): Promise<void> {
    console.log(chalk.yellow(`⚡ Executing recovery ${recoveryId.substring(0, 10)}...`));
    
    try {
      const tx = await this.contract.executeRecovery(recoveryId);
      console.log(chalk.gray(`   Transaction: ${tx.hash}`));
      
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        console.log(chalk.green(`✅ Recovery executed successfully`));
        console.log(chalk.gray(`   Block: ${receipt.blockNumber}`));
        console.log(chalk.gray(`   Gas used: ${receipt.gasUsed.toString()}`));
      } else {
        throw new Error('Transaction failed');
      }
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to execute recovery: ${error.message}`));
      throw error;
    }
  }

  /**
   * Check if recovery key is valid
   */
  async isRecoveryKeyValid(keyId: string): Promise<boolean> {
    try {
      return await this.contract.isRecoveryKeyValid(keyId);
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to check key validity: ${error.message}`));
      return false;
    }
  }

  /**
   * Get Safe registration info
   */
  async getSafeInfo(safeAddress: string): Promise<{
    registered: boolean;
    minimumKeys: number;
    currentKeyCount: number;
  }> {
    try {
      const [currentKeyCount, recoveryKeys, safeConfig] = await Promise.all([
        this.contract.getActiveKeyCount(safeAddress),
        this.contract.getRecoveryKeys(safeAddress),
        this.contract.getSafeConfig(safeAddress)
      ]);
      
      return {
        registered: safeConfig.isRegistered,
        minimumKeys: Number(safeConfig.minKeysRequired) || 3,
        currentKeyCount: Number(currentKeyCount)
      };
    } catch (error: any) {
      console.log(chalk.red(`❌ Failed to get Safe info: ${error.message}`));
      throw error;
    }
  }

  /**
   * Convert RecoveryPurpose enum to integer
   */
  private purposeToInt(purpose: RecoveryPurpose): number {
    switch (purpose) {
      case RecoveryPurpose.ADD_SIGNER: return 0;
      case RecoveryPurpose.ADD_RECOVERY_KEYS: return 1;
      case RecoveryPurpose.BOTH: return 2;
      default: return 2;
    }
  }

  /**
   * Convert integer to RecoveryPurpose enum
   */
  private intToPurpose(purpose: number): RecoveryPurpose {
    switch (purpose) {
      case 0: return RecoveryPurpose.ADD_SIGNER;
      case 1: return RecoveryPurpose.ADD_RECOVERY_KEYS;
      case 2: return RecoveryPurpose.BOTH;
      default: return RecoveryPurpose.BOTH;
    }
  }

  /**
   * Get contract address
   */
  getContractAddress(): string {
    return this.contractAddress;
  }

  /**
   * Get contract instance
   */
  getContract(): ethers.Contract {
    return this.contract;
  }
}