import { ethers } from "ethers";
import crypto from "crypto";
import chalk from "chalk";
import { GPSLocation } from "./GPSPrecisionManager";

/**
 * Recovery key for the simplified system (GPS processing done off-chain)
 */
export interface RecoveryKey {
  keyId: string;                    // Unique recovery key identifier
  safe: string;                     // Owner Safe wallet address
  expectedAddress: string;          // EOA address that can initiate recovery
  validUntilBlock: number;         // Expiration block
  isUsed: boolean;                 // One-time use flag
  purpose: RecoveryPurpose;        // What this key can accomplish
  hint: string;                    // User hint (not GPS data)
  stretchedCount: number;          // Number stretched keys updated
}

/**
 * GPS recovery key with off-chain GPS data for generation
 */
export interface GPSRecoveryKey extends RecoveryKey {
  location: GPSLocation;            // GPS coordinates (used off-chain only)
  generationDuration: number;       // Time spent generating (seconds) 
  createdAt: number;               // Creation timestamp
}

/**
 * Recovery purpose for dual-purpose system
 */
export enum RecoveryPurpose {
  ADD_SIGNER = 0,          // Can only add new signer
  ADD_RECOVERY_KEYS = 1,   // Can only add new recovery keys
  BOTH = 2                 // Can do both operations
}

/**
 * Key generation parameters
 */
export interface KeyGenerationParams {
  location: GPSLocation;
  generationTimeSeconds: number;   // 60-180 seconds (1-3 minutes)
  purpose: RecoveryPurpose;
  hint: string;
  safeAddress: string;
  validityBlocks: number;          // How long key is valid
}

/**
 * Recovery key generator for GPS-only multi-key system
 * Implements 1-3 minute key generation with GPS precision <10m
 */
export class RecoveryKeyGenerator {
  private static readonly MIN_GENERATION_TIME = 3;   // 3 seconds (symbolic)
  private static readonly MAX_GENERATION_TIME = 10;  // 10 seconds (symbolic)
  private static readonly DEFAULT_VALIDITY_BLOCKS = 6 * 30 * 24 * 60 * 4; // ~6 months
  private provider?: ethers.Provider;

  constructor(provider?: ethers.Provider) {
    this.provider = provider;
  }

  /**
   * Generate multiple GPS recovery keys
   */
  async generateMultipleKeys(
    location: GPSLocation,
    keyCount: number,
    generationTimePerKey: number,
    safeAddress: string,
    purpose: RecoveryPurpose = RecoveryPurpose.BOTH,
    baseHint: string = ""
  ): Promise<GPSRecoveryKey[]> {

    if (keyCount < 3) {
      throw new Error("Minimum 3 recovery keys required");
    }

    console.log(chalk.yellow(`🔑 Generating ${keyCount} GPS recovery keys...`));
    console.log(chalk.gray(`📍 Location: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`));
    console.log(chalk.gray(`⏱️  Generation time: ${generationTimePerKey}s per key (symbolic)`));

    const keys: GPSRecoveryKey[] = [];
    const startTime = Date.now();

    for (let i = 0; i < keyCount; i++) {
      console.log(chalk.blue(`\n🔑 Generating key ${i + 1}/${keyCount}...`));

      const keyStartTime = Date.now();

      // Generate unique key ID for this generation session
      const keyId = this.generateKeyId(location, i, startTime);

      // Create hint for this specific key
      const hint = `${baseHint} - Key ${i + 1}`;

      // Generate the key with specified generation time
      const key = await this.generateSingleKey({
        location,
        generationTimeSeconds: generationTimePerKey,
        purpose,
        hint,
        safeAddress,
        validityBlocks: RecoveryKeyGenerator.DEFAULT_VALIDITY_BLOCKS
      }, keyId);

      keys.push(key);

      const elapsed = (Date.now() - keyStartTime) / 1000;
      console.log(chalk.green(`✅ Key ${i + 1} generated in ${elapsed.toFixed(1)}s`));
      console.log(chalk.gray(`   Address: ${key.expectedAddress}`));
    }

    const totalTime = (Date.now() - startTime) / 1000;
    console.log(chalk.green(`\n🎉 All ${keyCount} keys generated in ${totalTime.toFixed(1)}s total`));

    return keys;
  }

  /**
   * Generate a single GPS recovery key
   */
  async generateSingleKey(
    params: KeyGenerationParams,
    keyId?: string
  ): Promise<GPSRecoveryKey> {

    this.validateGenerationParams(params);

    const generatedKeyId = keyId || this.generateKeyId(params.location, 0, Date.now());

    console.log(chalk.yellow(`🔄 Generating recovery key (${params.generationTimeSeconds}s - symbolic)...`));

    // Start key generation process
    const startTime = Date.now();

    // Create time-based seed that depends on generation duration
    const timeSeed = await this.createTimeSeed(params.generationTimeSeconds);

    // Create location seed with high precision
    const locationSeed = this.createLocationSeed(params.location);

    // Combine all entropy sources
    const combinedSeed = this.combineSeed(locationSeed, timeSeed, generatedKeyId);

    // Calculate iterations based on generation time (more time = more security)
    const iterations = this.calculateIterations(params.generationTimeSeconds);

    // Apply key stretching
    const stretchedKey = await this.stretchKey(combinedSeed, iterations);

    // Derive the recovery wallet
    const recoveryWallet = await this.deriveWallet(stretchedKey);

    // Ensure we actually spent the requested generation time
    await this.ensureGenerationTime(startTime, params.generationTimeSeconds);

    const actualDuration = (Date.now() - startTime) / 1000;

    console.log(chalk.green(`✅ Key generated: ${recoveryWallet.address}`));
    console.log(chalk.gray(`   Iterations: ${iterations.toLocaleString()}`));
    console.log(chalk.gray(`   Duration: ${actualDuration.toFixed(1)}s`));

    return {
      keyId: generatedKeyId,
      safe: params.safeAddress,
      expectedAddress: recoveryWallet.address,
      location: params.location,
      generationDuration: Math.round(actualDuration),
      createdAt: Date.now(),
      validUntilBlock: await this.getCurrentBlock() + params.validityBlocks,
      isUsed: false,
      purpose: params.purpose,
      hint: params.hint,
      stretchedCount: 100000
    };
  }

  /**
   * Regenerate a recovery key from stored parameters
   * Used during recovery to derive the same private key
   */
  async regenerateKey(
    keyData: GPSRecoveryKey,
    currentLocation: GPSLocation
  ): Promise<ethers.Wallet> {

    console.log(chalk.yellow(`🔄 Regenerating recovery key...`));

    // Validate location matches within precision
    const distance = this.calculateDistance(keyData.location, currentLocation);
    if (distance > keyData.location.precision) {
      throw new Error(`Location mismatch: ${distance.toFixed(1)}m > ${keyData.location.precision}m precision`);
    }

    // Recreate the same generation process
    const timeSeed = await this.createTimeSeed(keyData.generationDuration);
    const locationSeed = this.createLocationSeed(keyData.location); // Use original location
    const combinedSeed = this.combineSeed(locationSeed, timeSeed, keyData.keyId);

    const iterations = this.calculateIterations(keyData.generationDuration);
    const stretchedKey = await this.stretchKey(combinedSeed, iterations);
    const recoveryWallet = await this.deriveWallet(stretchedKey);

    // Verify the address matches
    if (recoveryWallet.address.toLowerCase() !== keyData.expectedAddress.toLowerCase()) {
      throw new Error(`Address mismatch: Generated ${recoveryWallet.address}, expected ${keyData.expectedAddress}`);
    }

    console.log(chalk.green(`✅ Recovery key regenerated successfully`));
    return recoveryWallet;
  }

  /**
   * Validate key generation parameters
   */
  private validateGenerationParams(params: KeyGenerationParams): void {
    // Allow flexible generation time for simulator
    if (params.generationTimeSeconds < 1) {
      throw new Error(`Generation time too short: ${params.generationTimeSeconds}s < 1s`);
    }

    if (params.generationTimeSeconds > 300) { // Max 5 minutes for any case
      throw new Error(`Generation time too long: ${params.generationTimeSeconds}s > 300s`);
    }

    if (params.location.precision > 10) {
      throw new Error(`GPS precision too low: ${params.location.precision}m > 10m required`);
    }

    if (!ethers.isAddress(params.safeAddress)) {
      throw new Error(`Invalid Safe address: ${params.safeAddress}`);
    }
  }

  /**
   * Generate unique key ID
   */
  private generateKeyId(location: GPSLocation, index: number, timestamp: number): string {
    const data = `${location.latitude}_${location.longitude}_${index}_${timestamp}`;
    return ethers.keccak256(ethers.toUtf8Bytes(data));
  }

  /**
   * Create time-based seed from generation duration
   */
  private async createTimeSeed(durationSeconds: number): Promise<Buffer> {
    // Create seed based on generation duration
    // More duration = different seed pattern
    const timeData = Buffer.from(`duration_${durationSeconds}`, 'utf8');
    return crypto.createHash('sha256').update(timeData).digest();
  }

  /**
   * Create location seed with GPS coordinates
   */
  private createLocationSeed(location: GPSLocation): Buffer {
    // Use high precision coordinates (6 decimal places)
    const lat = Math.round(location.latitude * 1000000);
    const lng = Math.round(location.longitude * 1000000);
    const precision = Math.round(location.precision);

    const locationData = `${lat},${lng},${precision}`;
    return crypto.createHash('sha256').update(locationData, 'utf8').digest();
  }

  /**
   * Combine all seed sources
   */
  private combineSeed(locationSeed: Buffer, timeSeed: Buffer, keyId: string): Buffer {
    const keyIdBuffer = Buffer.from(keyId.replace('0x', ''), 'hex');
    const combined = Buffer.concat([locationSeed, timeSeed, keyIdBuffer]);
    return crypto.createHash('sha256').update(combined).digest();
  }

  /**
   * Calculate PBKDF2 iterations based on generation time
   * More generation time = more iterations = stronger security
   */
  private calculateIterations(durationSeconds: number): number {
    // Base iterations: 1000 per second of generation time
    // Minimum 60,000 iterations (1 minute)
    // Maximum 180,000 iterations (3 minutes)
    const baseIterations = durationSeconds * 1000;

    // Add bonus iterations for longer generation times
    const bonusIterations = Math.max(0, (durationSeconds - 60) * 500);

    return baseIterations + bonusIterations;
  }

  /**
   * Apply PBKDF2 key stretching
   */
  private async stretchKey(seed: Buffer, iterations: number): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(seed, 'geosafe-v2', iterations, 32, 'sha256', (err, derivedKey) => {
        if (err) reject(err);
        else resolve(derivedKey);
      });
    });
  }

  /**
   * Derive wallet from stretched key
   */
  private async deriveWallet(stretchedKey: Buffer): Promise<ethers.Wallet> {
    // Use the stretched key directly as private key entropy
    const privateKey = '0x' + stretchedKey.toString('hex');
    return new ethers.Wallet(privateKey);
  }

  /**
   * Ensure minimum generation time is spent (symbolic for simulator)
   */
  private async ensureGenerationTime(startTime: number, targetSeconds: number): Promise<void> {
    const elapsed = (Date.now() - startTime) / 1000;
    if (elapsed < targetSeconds) {
      const remaining = targetSeconds - elapsed;
      console.log(chalk.gray(`⏳ Simulating ${remaining.toFixed(1)}s generation time...`));
      await new Promise(resolve => setTimeout(resolve, remaining * 1000));
    }
  }

  /**
   * Calculate distance between GPS locations
   */
  private calculateDistance(loc1: GPSLocation, loc2: GPSLocation): number {
    const R = 6371000; // Earth's radius in meters
    const φ1 = loc1.latitude * Math.PI / 180;
    const φ2 = loc2.latitude * Math.PI / 180;
    const Δφ = (loc2.latitude - loc1.latitude) * Math.PI / 180;
    const Δλ = (loc2.longitude - loc1.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) *
      Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * Get current block number from blockchain
   */
  private async getCurrentBlock(): Promise<number> {
    if (this.provider) {
      try {
        return await this.provider.getBlockNumber();
      } catch (error) {
        console.log(chalk.yellow('⚠️  Failed to get block number, using fallback'));
      }
    }
    // Fallback calculation (~15s per block on Ethereum)
    return Math.floor(Date.now() / 15000);
  }
}