{"network": "sepolia", "contractAddress": "0x062d87C9BB40b57358a528C685b6cfFDFD6BfF63", "deployerAddress": "0x5c66d481Df460717902dB24F1a3F3Bb62F785e72", "deploymentBlock": 8887910, "deploymentHash": "0x16fa5513e1bb60bc3d260d0a213123f2673e2ad7c10dd79b9e384337f921ebde", "timestamp": "2025-08-01T07:20:51.601Z", "version": "2.0.0", "improvements": ["Gas optimization with packed structs", "Removed unused code and debug functions", "Added comprehensive input validation", "Improved reentrancy protection", "Better error handling with specific error types", "Added safe configuration management", "Optimized loops and storage access", "Enhanced signature verification"], "constants": {"DEFAULT_RECOVERY_DELAY": "0", "MIN_RECOVERY_KEYS": "2", "DEFAULT_MIN_KEYS": "3", "MAX_RECOVERY_KEYS": "50"}}