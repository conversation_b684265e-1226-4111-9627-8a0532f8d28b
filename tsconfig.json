{"compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "typeRoots": ["./node_modules/@types"]}, "include": ["./scripts/**/*", "./test/**/*", "./src/**/*", "./hardhat.config.ts"], "exclude": ["node_modules", "dist", "artifacts", "cache"]}